# Logo Usage Guide - PSD

## Logo Files

- **Main Logo**: `public/logo.png` - <PERSON>go ch<PERSON>h của ứng dụng
- **Favicon**: `src/app/favicon.ico` - Icon hiển thị trên tab browser

## Logo Component

Ứng dụng có một component Logo tái sử dụng tại `src/components/ui/logo.tsx` với các tùy chọn:

### Props

- `size`: Kích thước logo (`"sm" | "md" | "lg" | "xl"`)
  - `sm`: 24x24px với text `text-lg`
  - `md`: 32x32px với text `text-xl` (mặc định)
  - `lg`: 48x48px với text `text-2xl`
  - `xl`: 80x80px với text `text-3xl`

- `showText`: Hiển thị text "PSD" bên cạnh logo (mặc định: `true`)
- `href`: <PERSON><PERSON><PERSON> c<PERSON>, logo sẽ trở thành link
- `className`: CSS classes tùy chỉnh

### Ví dụ sử dụng

```tsx
import { Logo } from "@/components/ui/logo"

// Logo cơ bản
<Logo />

// Logo lớn không có text
<Logo size="xl" showText={false} />

// Logo với link
<Logo href="/dashboard" size="md" />

// Logo với style tùy chỉnh
<Logo className="text-white hover:text-gray-200" />
```

## Vị trí sử dụng Logo

1. **Sidebar Dashboard** (`src/components/layout/sidebar.tsx`)
   - Size: `md`
   - Có link về dashboard
   - Màu trắng

2. **Trang đăng nhập** (`src/app/auth/signin/page.tsx`)
   - Size: `xl`
   - Có text "PSD"
   - Căn giữa

3. **Favicon** (`src/app/layout.tsx`)
   - Được cấu hình trong metadata
   - Hiển thị trên tab browser

4. **Header Component** (`src/components/layout/header.tsx`)
   - Component tùy chọn cho các trang không có sidebar

## Cập nhật Logo

Để thay đổi logo:

1. Thay thế file `public/logo.png` bằng logo mới
2. Đảm bảo logo có tỷ lệ vuông (1:1) để hiển thị tốt
3. Kích thước khuyến nghị: ít nhất 256x256px
4. Format: PNG với background trong suốt

## Metadata

Logo được cấu hình trong `src/app/layout.tsx`:

```tsx
export const metadata: Metadata = {
  title: "PSD",
  description: "Hệ thống quản lý doanh nghiệp PSD",
  icons: {
    icon: "/logo.png",
    shortcut: "/logo.png",
    apple: "/logo.png",
  },
}
```

## Responsive Design

Logo component tự động responsive:
- Trên mobile: có thể ẩn text để tiết kiệm không gian
- Trên desktop: hiển thị đầy đủ logo và text
- Sử dụng Next.js Image component để tối ưu hiệu suất
