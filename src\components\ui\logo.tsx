import Link from "next/link"
import { cn } from "@/lib/utils"

interface LogoProps {
  size?: "sm" | "md" | "lg" | "xl"
  showText?: boolean
  href?: string
  className?: string
  textClassName?: string
}

const sizeMap = {
  sm: { width: 24, height: 24, textSize: "text-lg" },
  md: { width: 32, height: 32, textSize: "text-xl" },
  lg: { width: 48, height: 48, textSize: "text-2xl" },
  xl: { width: 80, height: 80, textSize: "text-3xl" },
}

export function Logo({
  size = "md",
  showText = true,
  href,
  className,
  textClassName
}: LogoProps) {
  const { width, height, textSize } = sizeMap[size]

  const logoContent = (
    <div className={cn("flex items-center gap-3", className)}>
      {/* Logo từ file public/logo.png */}
      <div
        className="flex items-center justify-center rounded-md overflow-hidden bg-white shadow-sm border border-gray-200"
        style={{ width, height, minWidth: width, minHeight: height }}
      >
        <img
          src="/logo.png"
          alt="PSD Logo"
          width={width}
          height={height}
          className="object-contain w-full h-full p-1"
        />
      </div>
      {showText && (
        <span className={cn("font-bold", textSize, textClassName || "text-gray-900")}>
          PSD
        </span>
      )}
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="hover:opacity-80 transition-opacity">
        {logoContent}
      </Link>
    )
  }

  return logoContent
}
