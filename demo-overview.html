<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSD ERP System - Demo Overview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .content-transition { transition: margin-left 0.3s ease-in-out; }
        .logo-container {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation Tabs -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4">
            <nav class="flex space-x-8">
                <button onclick="showPage('login')" class="py-4 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium">
                    Đ<PERSON><PERSON> nhập
                </button>
                <button onclick="showPage('dashboard')" class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium">
                    Dashboard
                </button>
                <button onclick="showPage('products')" class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium">
                    Sản phẩm
                </button>
                <button onclick="showPage('orders')" class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium">
                    Đơn hàng
                </button>
                <button onclick="showPage('customers')" class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium">
                    Khách hàng
                </button>
            </nav>
        </div>
    </div>

    <!-- Login Page -->
    <div id="login-page" class="page-content">
        <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <div>
                    <div class="flex justify-center">
                        <!-- Logo Component -->
                        <div class="flex items-center gap-3">
                            <div class="flex items-center justify-center rounded-md overflow-hidden bg-white shadow-sm border border-gray-200 logo-container" style="width: 80px; height: 80px;">
                                <span class="text-white font-bold text-2xl">PSD</span>
                            </div>
                            <span class="font-bold text-3xl text-gray-900">PSD</span>
                        </div>
                    </div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Đăng nhập hệ thống
                    </h2>
                    <p class="mt-2 text-center text-sm text-gray-600">
                        Hệ thống quản lý doanh nghiệp
                    </p>
                </div>
                <form class="mt-8 space-y-6">
                    <div class="rounded-md shadow-sm -space-y-px">
                        <div>
                            <input type="text" placeholder="Tên đăng nhập hoặc email" 
                                   class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm">
                        </div>
                        <div>
                            <input type="password" placeholder="Mật khẩu" 
                                   class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm">
                        </div>
                    </div>
                    <div>
                        <button type="button" onclick="showPage('dashboard')" 
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Đăng nhập
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Dashboard Page -->
    <div id="dashboard-page" class="page-content hidden">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <div class="w-64 bg-gray-800 text-white">
                <div class="p-4">
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md overflow-hidden bg-white shadow-sm border border-gray-200 logo-container" style="width: 32px; height: 32px;">
                            <span class="text-white font-bold text-sm">PSD</span>
                        </div>
                        <span class="font-bold text-xl text-white">PSD</span>
                    </div>
                </div>
                <nav class="mt-8">
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="package" class="w-5 h-5 mr-3"></i>
                        Sản phẩm
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        Đơn hàng
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        Khách hàng
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="warehouse" class="w-5 h-5 mr-3"></i>
                        Kho hàng
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="bar-chart" class="w-5 h-5 mr-3"></i>
                        Báo cáo
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Header -->
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center justify-between px-6 py-4">
                        <h1 class="text-2xl font-semibold text-gray-900">Dashboard</h1>
                        <div class="flex items-center space-x-4">
                            <button class="text-gray-500 hover:text-gray-700">
                                <i data-lucide="bell" class="w-5 h-5"></i>
                            </button>
                            <div class="flex items-center space-x-2">
                                <img class="w-8 h-8 rounded-full bg-gray-300" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%236B7280'/%3E%3Ctext x='16' y='20' text-anchor='middle' fill='white' font-size='12'%3EA%3C/text%3E%3C/svg%3E" alt="User">
                                <span class="text-sm font-medium text-gray-700">Admin User</span>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Dashboard Content -->
                <main class="flex-1 overflow-y-auto p-6">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Tổng sản phẩm</p>
                                    <p class="text-2xl font-semibold text-gray-900">1,234</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <i data-lucide="shopping-cart" class="w-6 h-6 text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Đơn hàng hôm nay</p>
                                    <p class="text-2xl font-semibold text-gray-900">56</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-yellow-100 rounded-lg">
                                    <i data-lucide="users" class="w-6 h-6 text-yellow-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Khách hàng</p>
                                    <p class="text-2xl font-semibold text-gray-900">789</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Doanh thu tháng</p>
                                    <p class="text-2xl font-semibold text-gray-900">₫125M</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Đơn hàng gần đây</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã đơn</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khách hàng</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng tiền</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#ORD-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Nguyễn Văn A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₫2,500,000</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Hoàn thành</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18/07/2025</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#ORD-002</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Trần Thị B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₫1,800,000</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Đang xử lý</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18/07/2025</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Products Page -->
    <div id="products-page" class="page-content hidden">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar (same as dashboard) -->
            <div class="w-64 bg-gray-800 text-white">
                <div class="p-4">
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md overflow-hidden bg-white shadow-sm border border-gray-200 logo-container" style="width: 32px; height: 32px;">
                            <span class="text-white font-bold text-sm">PSD</span>
                        </div>
                        <span class="font-bold text-xl text-white">PSD</span>
                    </div>
                </div>
                <nav class="mt-8">
                    <a href="#" onclick="showPage('dashboard')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 bg-gray-700 text-white">
                        <i data-lucide="package" class="w-5 h-5 mr-3"></i>
                        Sản phẩm
                    </a>
                    <a href="#" onclick="showPage('orders')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        Đơn hàng
                    </a>
                    <a href="#" onclick="showPage('customers')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        Khách hàng
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center justify-between px-6 py-4">
                        <h1 class="text-2xl font-semibold text-gray-900">Quản lý sản phẩm</h1>
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                            Thêm sản phẩm
                        </button>
                    </div>
                </header>

                <main class="flex-1 overflow-y-auto p-6">
                    <!-- Search and Filter -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1">
                                <input type="text" placeholder="Tìm kiếm sản phẩm..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>Tất cả danh mục</option>
                                <option>Điện tử</option>
                                <option>Thời trang</option>
                                <option>Gia dụng</option>
                            </select>
                            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>Tất cả trạng thái</option>
                                <option>Còn hàng</option>
                                <option>Hết hàng</option>
                                <option>Ngừng bán</option>
                            </select>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh mục</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tồn kho</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-gray-200 rounded-md mr-4"></div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">iPhone 15 Pro</div>
                                                    <div class="text-sm text-gray-500">Điện thoại thông minh</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">IP15P-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Điện tử</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₫29,990,000</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">25</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Còn hàng</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Sửa</button>
                                            <button class="text-red-600 hover:text-red-900">Xóa</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-gray-200 rounded-md mr-4"></div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">MacBook Air M3</div>
                                                    <div class="text-sm text-gray-500">Laptop</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MBA-M3-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Điện tử</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₫32,990,000</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Hết hàng</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Sửa</button>
                                            <button class="text-red-600 hover:text-red-900">Xóa</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.add('hidden');
            });

            // Show selected page
            document.getElementById(pageId + '-page').classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('nav button').forEach(btn => {
                btn.className = btn.className.replace('border-indigo-500 text-indigo-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            });

            if (event && event.target) {
                event.target.className = event.target.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-indigo-500 text-indigo-600');
            }

            // Re-initialize icons after page change
            setTimeout(() => lucide.createIcons(), 100);
        }
    </script>

    <!-- Orders Page -->
    <div id="orders-page" class="page-content hidden">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <div class="w-64 bg-gray-800 text-white">
                <div class="p-4">
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md overflow-hidden bg-white shadow-sm border border-gray-200 logo-container" style="width: 32px; height: 32px;">
                            <span class="text-white font-bold text-sm">PSD</span>
                        </div>
                        <span class="font-bold text-xl text-white">PSD</span>
                    </div>
                </div>
                <nav class="mt-8">
                    <a href="#" onclick="showPage('dashboard')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" onclick="showPage('products')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="package" class="w-5 h-5 mr-3"></i>
                        Sản phẩm
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 bg-gray-700 text-white">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        Đơn hàng
                    </a>
                    <a href="#" onclick="showPage('customers')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        Khách hàng
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center justify-between px-6 py-4">
                        <h1 class="text-2xl font-semibold text-gray-900">Quản lý đơn hàng</h1>
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                            Tạo đơn hàng
                        </button>
                    </div>
                </header>

                <main class="flex-1 overflow-y-auto p-6">
                    <!-- Filter Tabs -->
                    <div class="bg-white rounded-lg shadow mb-6">
                        <div class="border-b border-gray-200">
                            <nav class="flex space-x-8 px-6">
                                <button class="py-4 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium">Tất cả</button>
                                <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">Chờ xử lý</button>
                                <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">Đang giao</button>
                                <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">Hoàn thành</button>
                                <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">Đã hủy</button>
                            </nav>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã đơn</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khách hàng</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng tiền</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#ORD-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Nguyễn Văn A</div>
                                            <div class="text-sm text-gray-500"><EMAIL></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">iPhone 15 Pro x1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₫29,990,000</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Hoàn thành</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18/07/2025</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Xem</button>
                                            <button class="text-gray-600 hover:text-gray-900">In</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#ORD-002</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Trần Thị B</div>
                                            <div class="text-sm text-gray-500"><EMAIL></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MacBook Air M3 x1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₫32,990,000</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Đang giao</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">17/07/2025</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Xem</button>
                                            <button class="text-gray-600 hover:text-gray-900">In</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Customers Page -->
    <div id="customers-page" class="page-content hidden">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <div class="w-64 bg-gray-800 text-white">
                <div class="p-4">
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md overflow-hidden bg-white shadow-sm border border-gray-200 logo-container" style="width: 32px; height: 32px;">
                            <span class="text-white font-bold text-sm">PSD</span>
                        </div>
                        <span class="font-bold text-xl text-white">PSD</span>
                    </div>
                </div>
                <nav class="mt-8">
                    <a href="#" onclick="showPage('dashboard')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" onclick="showPage('products')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="package" class="w-5 h-5 mr-3"></i>
                        Sản phẩm
                    </a>
                    <a href="#" onclick="showPage('orders')" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        Đơn hàng
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 bg-gray-700 text-white">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        Khách hàng
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center justify-between px-6 py-4">
                        <h1 class="text-2xl font-semibold text-gray-900">Quản lý khách hàng</h1>
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                            Thêm khách hàng
                        </button>
                    </div>
                </header>

                <main class="flex-1 overflow-y-auto p-6">
                    <!-- Search -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="flex gap-4">
                            <div class="flex-1">
                                <input type="text" placeholder="Tìm kiếm khách hàng..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <button class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                                <i data-lucide="search" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Customers Table -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khách hàng</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Điện thoại</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Địa chỉ</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng đơn</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                                                    <span class="text-indigo-600 font-medium">NA</span>
                                                </div>
                                                <div class="text-sm font-medium text-gray-900">Nguyễn Văn A</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><EMAIL></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0901234567</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">123 Nguyễn Trãi, Q1, HCM</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5 đơn</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15/01/2025</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Xem</button>
                                            <button class="text-gray-600 hover:text-gray-900">Sửa</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-4">
                                                    <span class="text-pink-600 font-medium">TB</span>
                                                </div>
                                                <div class="text-sm font-medium text-gray-900">Trần Thị B</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><EMAIL></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0907654321</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">456 Lê Lợi, Q3, HCM</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3 đơn</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20/02/2025</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-indigo-600 hover:text-indigo-900 mr-3">Xem</button>
                                            <button class="text-gray-600 hover:text-gray-900">Sửa</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
</body>
</html>
